"""add_topic_finished_and_quiz_json_columns

Revision ID: 73033ad1e4f8
Revises: 747f4fb0d767
Create Date: 2025-08-12 13:55:59.592038

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '73033ad1e4f8'
down_revision: Union[str, Sequence[str], None] = '747f4fb0d767'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add topic_finished column to roadmap_topics table
    op.add_column(
        'roadmap_topics',
        sa.Column('topic_finished', sa.<PERSON>(), nullable=False, server_default=sa.text('FALSE'))
    )

    # Add quiz_json column to roadmap_topics table
    op.add_column(
        'roadmap_topics',
        sa.Column('quiz_json', postgresql.JSON(astext_type=sa.Text()), nullable=True)
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Remove quiz_json column from roadmap_topics table
    op.drop_column('roadmap_topics', 'quiz_json')

    # Remove topic_finished column from roadmap_topics table
    op.drop_column('roadmap_topics', 'topic_finished')
