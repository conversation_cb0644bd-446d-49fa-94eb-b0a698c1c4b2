# settings.py
import os
from dotenv import load_dotenv

# Load once, globally
load_dotenv()

class Settings:
    def get(self, key: str, default=None):
        return os.getenv(key, default)

    @property
    def database_url(self):
        return self.get('DATABASE_URL')

    @property
    def google_api_key(self):
        return self.get('GOOGLE_API_KEY')

# Create a global instance
settings = Settings()
