import os
import json
import time
import logging
import re
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional

from langchain_google_genai import ChatG<PERSON>gleGenerativeAI
from langchain_core.messages import SystemMessage
from langchain.prompts import PromptTemplate
from app.core.settings import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GenerateTopicQuiz:
    """
    Generates topic-level quizzes in JSON format using an LLM.
    Includes retry logic, strict JSON parsing, and validation.
    """

    QUIZ_PROMPT_TEMPLATE = """
    You are an expert quiz generator for technical subjects.  
    Given the following topic content in MDX format, generate a **topic-level quiz** in JSON format.

    CONTENT (MDX):
    ---
    {context}
    ---

    TASK:
    1. Create **5–10 multiple-choice questions** that test the student's understanding of the above topic.
    2. Each question must:
       - Be **clear and concise**
       - Have **4 answer options** (A, B, C, D)
       - Have exactly **one correct answer**
       - Avoid trivial questions — focus on **key concepts and applied understanding**
    3. Return results in **valid JSON** with this schema:
    [
      {{
        "question": "string",
        "options": ["string", "string", "string", "string"],
        "answer": number,  // index of correct option (0–3)
        "explanation": "string"  // why the answer is correct
      }}
    ]
    4. Keep **questions and explanations self-contained** so they make sense without the original content.
    5. DO NOT add extra commentary or text outside the JSON.
    """

    def __init__(
        self,
        api_key: Optional[str] = settings.google_api_key,
        model: str = "gemini-1.5-flash",
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        Args:
            api_key: Google Generative AI API key (or set GOOGLE_API_KEY env var)
            model: LLM model name
            max_retries: Max retry attempts on failure
            retry_delay: Delay between retries in seconds
        """

        self.llm = ChatGoogleGenerativeAI(
            model=model, google_api_key=api_key, temperature=0.7, max_tokens=8192
        )
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def _parse_and_validate_response(
        self, response_text: str
    ) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """
        Attempts to parse and validate JSON from model output.
        Strips non-JSON text if necessary.
        """
        try:
            # Extract JSON block if extra text is present
            json_match = re.search(r"\[.*\]", response_text, re.DOTALL)
            if not json_match:
                return False, None, "No JSON array detected in response."

            json_str = json_match.group(0)
            data = json.loads(json_str)

            # Validate schema
            if not isinstance(data, list):
                return False, None, "Top-level JSON must be a list."

            if len(data) < 5 or len(data) > 10:
                return False, None, f"Quiz must have 5-10 questions, got {len(data)}."

            for idx, q in enumerate(data):
                if not all(
                    k in q for k in ("question", "options", "answer", "explanation")
                ):
                    return False, None, f"Missing required keys in question {idx+1}."

                # Validate question text
                if not isinstance(q["question"], str) or len(q["question"].strip()) < 10:
                    return False, None, f"Question {idx+1} must be a non-empty string with at least 10 characters."

                # Validate options
                if not isinstance(q["options"], list) or len(q["options"]) != 4:
                    return False, None, f"Question {idx+1} must have exactly 4 options."

                for opt_idx, option in enumerate(q["options"]):
                    if not isinstance(option, str) or len(option.strip()) < 1:
                        return False, None, f"Question {idx+1}, option {opt_idx+1} must be a non-empty string."

                # Validate answer index
                if not isinstance(q["answer"], int) or not (0 <= q["answer"] <= 3):
                    return (
                        False,
                        None,
                        f"Question {idx+1} answer index must be between 0 and 3.",
                    )

                # Validate explanation
                if not isinstance(q["explanation"], str) or len(q["explanation"].strip()) < 10:
                    return False, None, f"Question {idx+1} explanation must be a non-empty string with at least 10 characters."

            return True, data, None
        except json.JSONDecodeError as e:
            return False, None, f"JSON parsing error: {str(e)}"
        except Exception as e:
            return False, None, f"Validation error: {str(e)}"

    def generate(
        self, context: str
    ) -> Tuple[bool, Optional[Dict[Any, Any]], Optional[str], float]:
        """
        Generates a quiz from topic content using the LLM.

        Args:
            context: The MDX content for the topic

        Returns:
            (success, parsed_data, error_message, generation_time)
        """
        start_time = time.time()

        # Input validation
        if not context or not isinstance(context, str):
            return False, None, "Context must be a non-empty string", 0.0

        if len(context.strip()) < 100:
            return False, None, "Context too short to generate meaningful quiz", 0.0

        prompt = PromptTemplate(
            input_variables=["context"], template=self.QUIZ_PROMPT_TEMPLATE
        )

        formatted_prompt = prompt.format(context=context)
        messages = [SystemMessage(content=formatted_prompt)]

        for attempt in range(self.max_retries):
            try:
                logger.info(
                    f"Generating quiz (attempt {attempt + 1}/{self.max_retries})..."
                )
                response = self.llm.invoke(messages)

                success, parsed_data, parse_error = self._parse_and_validate_response(
                    response.content
                )
                if success:
                    generation_time = time.time() - start_time
                    logger.info(
                        f"Quiz generated successfully in {generation_time:.2f}s"
                    )
                    return True, parsed_data, None, generation_time

                logger.warning(f"Attempt {attempt + 1} failed: {parse_error}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)

            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed with exception: {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)

        return (
            False,
            None,
            f"Failed after {self.max_retries} attempts.",
            time.time() - start_time,
        )
