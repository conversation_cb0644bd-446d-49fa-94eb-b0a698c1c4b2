"""Add topic_finished column to roadmap_topics

Revision ID: add_topic_finished
Revises: [previous_revision_id]
Create Date: 2025-08-12 [current_time]

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_topic_finished'
down_revision = '[previous_revision_id]'  # Replace with your latest revision
branch_labels = None
depends_on = None

def upgrade():
    op.add_column(
        'roadmap_topics',
        sa.Column('topic_finished', sa.<PERSON>(), nullable=False, server_default=sa.text('FALSE'))
    )

def downgrade():
    op.drop_column('roadmap_topics', 'topic_finished')