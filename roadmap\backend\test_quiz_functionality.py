#!/usr/bin/env python3
"""
Test script to verify quiz functionality and database schema
"""

import sys
import os
import json
import requests
import time

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.db.connection import SessionLocal
from app.models.roadmap import RoadmapRequest, RoadmapDay, RoadmapTopic
from app.core.roadmap.generate_topic_quiz import GenerateTopicQuiz

def test_database_schema():
    """Test that the database schema includes the new columns"""
    print("Testing database schema...")
    
    db = SessionLocal()
    try:
        # Try to query a topic with the new columns
        topic = db.query(RoadmapTopic).first()
        if topic:
            print(f"✓ Found topic: {topic.title}")
            print(f"✓ topic_finished field: {topic.topic_finished}")
            print(f"✓ quiz_json field: {topic.quiz_json}")
        else:
            print("ℹ No topics found in database")
        
        print("✓ Database schema test passed")
        return True
    except Exception as e:
        print(f"✗ Database schema test failed: {e}")
        return False
    finally:
        db.close()

def test_quiz_generation():
    """Test quiz generation functionality"""
    print("\nTesting quiz generation...")
    
    # Sample MDX content for testing
    sample_content = """
# Introduction to Python Programming

Python is a high-level, interpreted programming language known for its simplicity and readability. 
It was created by Guido van Rossum and first released in 1991.

## Key Features

1. **Easy to Learn**: Python has a simple syntax that makes it easy for beginners
2. **Versatile**: Can be used for web development, data science, automation, and more
3. **Large Community**: Extensive libraries and community support
4. **Cross-platform**: Runs on Windows, macOS, and Linux

## Basic Syntax

Variables in Python are created by assignment:
```python
name = "Alice"
age = 25
```

Python uses indentation to define code blocks instead of curly braces.

## Data Types

Python has several built-in data types:
- Integers: `42`
- Floats: `3.14`
- Strings: `"Hello World"`
- Lists: `[1, 2, 3]`
- Dictionaries: `{"key": "value"}`

## Control Structures

Python supports standard control structures like if statements and loops:
```python
if age >= 18:
    print("Adult")
else:
    print("Minor")
```
"""
    
    try:
        quiz_generator = GenerateTopicQuiz()
        success, quiz_data, error_message, generation_time = quiz_generator.generate(sample_content)
        
        if success:
            print(f"✓ Quiz generated successfully in {generation_time:.2f}s")
            print(f"✓ Number of questions: {len(quiz_data)}")
            
            # Validate quiz structure
            for i, question in enumerate(quiz_data):
                if not all(k in question for k in ["question", "options", "answer", "explanation"]):
                    print(f"✗ Question {i+1} missing required fields")
                    return False
                if len(question["options"]) != 4:
                    print(f"✗ Question {i+1} doesn't have 4 options")
                    return False
                if not (0 <= question["answer"] <= 3):
                    print(f"✗ Question {i+1} has invalid answer index")
                    return False
            
            print("✓ Quiz structure validation passed")
            print(f"✓ Sample question: {quiz_data[0]['question']}")
            return True
        else:
            print(f"✗ Quiz generation failed: {error_message}")
            return False
    except Exception as e:
        print(f"✗ Quiz generation test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints"""
    print("\nTesting API endpoints...")
    
    base_url = "http://localhost:8000"
    
    try:
        # Test health check or docs endpoint
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✓ API server is responding")
            return True
        else:
            print(f"✗ API server returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ API endpoint test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Quiz Functionality Test Suite ===\n")
    
    tests = [
        ("Database Schema", test_database_schema),
        ("Quiz Generation", test_quiz_generation),
        ("API Endpoints", test_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name} test...")
        result = test_func()
        results.append((test_name, result))
        print()
    
    print("=== Test Results ===")
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Quiz functionality is production ready.")
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
