from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, validator
from app.db.connection import get_db
from app.core.roadmap.generate_roadmap import GenerateRoadmap
from app.models.roadmap import RoadmapRequest, RoadmapDay, RoadmapTopic, MDXFile
from app.utils.mdx_manager import mdx_manager
from app.utils.context_extractor import context_extractor
import json
import logging
from typing import Any, Optional, List
from datetime import datetime
import uuid
import asyncio
from app.core.roadmap.generate_topic_quiz import GenerateTopicQuiz

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["roadmap"])


class RoadmapCreateRequest(BaseModel):
    """Request model for creating a roadmap"""

    age: str = Field(..., min_length=1, max_length=50, description="User's age")
    currentField: str = Field(
        ..., min_length=1, max_length=200, description="Current field/industry"
    )
    currentRole: str = Field(
        ..., min_length=1, max_length=200, description="Current role"
    )
    daysPerWeek: int = Field(
        ..., ge=1, le=7, description="Days per week available for learning"
    )
    hoursPerDay: int = Field(
        ..., ge=1, le=24, description="Hours per day available for learning"
    )
    knowledgeDescription: str = Field(
        ...,
        min_length=1,
        max_length=2000,
        description="Description of current knowledge",
    )
    knowledgeLevel: int = Field(
        ..., ge=0, le=10, description="Knowledge level from 0-10"
    )
    topic: str = Field(..., min_length=1, max_length=500, description="Topic to learn")
    topicReason: str = Field(
        ..., min_length=1, max_length=2000, description="Reason for learning this topic"
    )
    totalDuration: str = Field(
        ..., min_length=1, max_length=100, description="Total duration for learning"
    )

    @validator("age")
    def validate_age(cls, v):
        if not v.strip():
            raise ValueError("Age cannot be empty")
        return v.strip()

    @validator("currentField", "currentRole", "topic")
    def validate_required_strings(cls, v):
        if not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class RoadmapResponse(BaseModel):
    """Response model for roadmap operations"""

    success: bool
    message: str
    data: Optional[Any] = None
    roadmap_id: Optional[str] = None
    status: Optional[str] = None


class RoadmapStatusResponse(BaseModel):
    """Response model for roadmap status"""

    success: bool
    roadmap_id: str
    status: str
    message: str
    progress: Optional[dict] = None
    error: Optional[str] = None


class TopicResponse(BaseModel):
    """Response model for individual topics"""

    topic_number: int
    title: str
    description: str
    estimated_time_hours: float
    content: str
    resources: List[str]
    topic_finished: bool = False
    mdx_file_path: Optional[str] = None


class DayResponse(BaseModel):
    """Response model for roadmap days"""

    day: int
    total_time_hours: float
    topics: List[TopicResponse]


class RoadmapDetailResponse(BaseModel):
    """Detailed response model for complete roadmap"""

    success: bool
    message: str
    roadmap_id: str
    status: str
    created_at: datetime
    summary: Optional[str] = None
    total_days: Optional[int] = None
    total_topics: Optional[int] = None
    context: Optional[dict] = None
    days: List[DayResponse]


class RoadmapListResponse(BaseModel):
    """Response model for list of roadmaps"""

    success: bool
    message: str
    roadmaps: Any


class QuizResponse(BaseModel):
    """Response model for quiz data"""

    success: bool
    message: str
    quiz_data: Optional[Any] = None
    topic_id: Optional[int] = None


class TopicCompletionRequest(BaseModel):
    """Request model for marking topic as completed"""

    completed: bool


class TopicCompletionResponse(BaseModel):
    """Response model for topic completion"""

    success: bool
    message: str
    topic_id: int
    completed: bool


@router.post("/roadmap", response_model=RoadmapResponse)
async def create_roadmap(
    payload: RoadmapCreateRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    """
    Create a new roadmap generation request

    This endpoint creates a roadmap request and starts the generation process in the background.
    The actual generation happens asynchronously to avoid timeout issues.
    """
    try:
        logger.info(f"Creating new roadmap request for topic: {payload.topic}")

        # Create roadmap request record
        roadmap_request = RoadmapRequest(
            age=payload.age,
            current_field=payload.currentField,
            current_role=payload.currentRole,
            days_per_week=payload.daysPerWeek,
            hours_per_day=payload.hoursPerDay,
            knowledge_description=payload.knowledgeDescription,
            knowledge_level=payload.knowledgeLevel,
            topic=payload.topic,
            topic_reason=payload.topicReason,
            total_duration=payload.totalDuration,
            status="pending",
        )

        db.add(roadmap_request)
        db.commit()
        db.refresh(roadmap_request)

        # Start background generation
        background_tasks.add_task(
            generate_roadmap_background, roadmap_request.id, payload.model_dump()
        )

        logger.info(
            f"Roadmap request created with ID: {roadmap_request.id}, UUID: {roadmap_request.uuid}"
        )

        return RoadmapResponse(
            success=True,
            message="Roadmap generation started. Use the roadmap_id to check status.",
            roadmap_id=roadmap_request.uuid,
            status="pending",
        )

    except Exception as e:
        logger.error(f"Error creating roadmap request: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating roadmap request: {str(e)}",
        )

async def generate_roadmap_background(roadmap_request_id: int, payload_data: dict):
    """Background task to generate roadmap content and quizzes"""
    from app.db.connection import SessionLocal
    from app.core.roadmap.generate_topic_quiz import GenerateTopicQuiz  

    db = SessionLocal()
    try:
        logger.info(f"Starting background generation for roadmap {roadmap_request_id}")

        # Get the roadmap request
        roadmap_request = (
            db.query(RoadmapRequest)
            .filter(RoadmapRequest.id == roadmap_request_id)
            .first()
        )
        if not roadmap_request:
            logger.error(f"Roadmap request {roadmap_request_id} not found")
            return

        # Update status to generating
        roadmap_request.status = "generating"
        db.commit()

        # Generate roadmap using LLM
        generator = GenerateRoadmap()
        success, roadmap_data, error_message, generation_time = generator.generate(
            payload_data
        )

        if not success:
            logger.error(f"Roadmap generation failed: {error_message}")
            roadmap_request.status = "failed"
            roadmap_request.error_message = error_message
            roadmap_request.generation_time_seconds = generation_time
            db.commit()
            return

        # Extract context
        context = context_extractor.extract_roadmap_context(roadmap_data)

        # Save roadmap data to database (this will create all topics)
        await save_roadmap_to_database(
            db, roadmap_request, roadmap_data, context, generation_time
        )

        # After successful roadmap creation, generate quizzes for all topics
        logger.info(f"Starting quiz generation for roadmap {roadmap_request_id}")
        roadmap_request.status = "generating_quizzes"
        db.commit()

        await generate_quizzes_for_roadmap(db, roadmap_request_id)

        # Update final status
        roadmap_request.status = "completed"
        db.commit()

        logger.info(f"Roadmap and quiz generation completed for request {roadmap_request_id}")

    except Exception as e:
        logger.error(f"Error in background generation: {str(e)}")
        try:
            roadmap_request = (
                db.query(RoadmapRequest)
                .filter(RoadmapRequest.id == roadmap_request_id)
                .first()
            )
            if roadmap_request:
                roadmap_request.status = "failed"
                roadmap_request.error_message = str(e)
                db.commit()
        except Exception as commit_error:
            logger.error(f"Error updating failed status: {str(commit_error)}")
    finally:
        db.close()


async def generate_quizzes_for_roadmap(db: Session, roadmap_request_id: int):
    """Generate quizzes for all topics in a roadmap"""
    try:
        # Initialize quiz generator
        quiz_generator = GenerateTopicQuiz()
        
        # Get all topics for this roadmap
        topics = (
            db.query(RoadmapTopic)
            .join(RoadmapDay)
            .filter(RoadmapDay.roadmap_request_id == roadmap_request_id)
            .all()
        )

        total_topics = len(topics)
        successful_quizzes = 0
        failed_quizzes = 0

        logger.info(f"Generating quizzes for {total_topics} topics in roadmap {roadmap_request_id}")

        for topic in topics:
            try:
                logger.info(f"Generating quiz for topic {topic.id}: {topic.title}")
                
                # Generate quiz using the topic's content
                success, quiz_data, error_message, generation_time = quiz_generator.generate(
                    topic.content
                )

                if success:
                    # Save quiz data to the topic
                    topic.quiz_json = quiz_data
                    successful_quizzes += 1
                    logger.info(f"Quiz generated successfully for topic {topic.id}")
                else:
                    logger.error(f"Quiz generation failed for topic {topic.id}: {error_message}")
                    # You might want to store the error or set a flag
                    topic.quiz_json = {"error": error_message, "generated_at": None}
                    failed_quizzes += 1

                # Commit after each topic to avoid losing progress
                db.commit()

            except Exception as e:
                logger.error(f"Exception generating quiz for topic {topic.id}: {str(e)}")
                topic.quiz_json = {"error": str(e), "generated_at": None}
                failed_quizzes += 1
                db.commit()

        logger.info(
            f"Quiz generation completed for roadmap {roadmap_request_id}: "
            f"{successful_quizzes} successful, {failed_quizzes} failed"
        )

    except Exception as e:
        logger.error(f"Error in generate_quizzes_for_roadmap: {str(e)}")
        raise


async def save_roadmap_to_database(
    db: Session,
    roadmap_request: RoadmapRequest,
    roadmap_data: dict,
    context: dict,
    generation_time: float,
):
    """Save generated roadmap data to database"""
    try:
        # Update roadmap request with metadata
        roadmap_request.generation_time_seconds = generation_time
        roadmap_request.extracted_context = context
        roadmap_request.total_days = len(roadmap_data.get("plan", []))
        roadmap_request.total_topics = sum(
            len(day.get("topics", [])) for day in roadmap_data.get("plan", [])
        )

        # Generate summary
        overview = context.get("overview", {})
        roadmap_request.summary = f"Learning roadmap for {roadmap_request.topic} over {roadmap_request.total_days} days with {roadmap_request.total_topics} topics, estimated {overview.get('total_estimated_hours', 0)} hours total."

        # Save days and topics
        for day_data in roadmap_data.get("plan", []):
            day = RoadmapDay(
                roadmap_request_id=roadmap_request.id,
                day_number=day_data.get("day", 0),
                total_time_hours=day_data.get("total_time_hours", 0),
            )
            db.add(day)
            db.flush()  # Get the day ID

            # Save topics for this day
            for topic_data in day_data.get("topics", []):
                # Generate MDX file path
                file_path, filename = mdx_manager.generate_file_path(
                    roadmap_request.uuid,
                    topic_data.get("topic_number", 0),
                    topic_data.get("title", "untitled"),
                )

                # Save MDX content to file
                content = topic_data.get("content", "")
                save_success, save_error, file_size, content_hash = (
                    mdx_manager.save_mdx_content(content, file_path)
                )

                # Create topic record
                topic = RoadmapTopic(
                    day_id=day.id,
                    topic_number=topic_data.get("topic_number", 0),
                    title=topic_data.get("title", ""),
                    description=topic_data.get("description", ""),
                    estimated_time_hours=topic_data.get("estimated_time_hours", 0),
                    content=content,
                    resources=topic_data.get("resources", []),
                    topic_finished=False,  # Initially not finished
                    mdx_file_path=file_path if save_success else None,
                    quiz_json=None,  # Will be populated later by quiz generation
                )
                db.add(topic)
                db.flush()  # Get the topic ID

                # Create MDX file record if save was successful
                if save_success:
                    mdx_file = MDXFile(
                        roadmap_request_id=roadmap_request.id,
                        file_path=file_path,
                        file_name=filename,
                        topic_id=topic.id,
                        file_size_bytes=file_size,
                        content_hash=content_hash,
                    )
                    db.add(mdx_file)

        db.commit()
        logger.info(f"Successfully saved roadmap data for request {roadmap_request.id}")

    except Exception as e:
        logger.error(f"Error saving roadmap to database: {str(e)}")
        db.rollback()
        raise


# Optional: Separate endpoint to regenerate quizzes for a specific roadmap
async def regenerate_roadmap_quizzes(roadmap_request_id: int):
    """Regenerate quizzes for all topics in a roadmap (useful for retries)"""
    from app.db.connection import SessionLocal
    
    db = SessionLocal()
    try:
        # Verify roadmap exists
        roadmap_request = (
            db.query(RoadmapRequest)
            .filter(RoadmapRequest.id == roadmap_request_id)
            .first()
        )
        
        if not roadmap_request:
            logger.error(f"Roadmap request {roadmap_request_id} not found")
            return False, "Roadmap not found"

        # Generate quizzes
        await generate_quizzes_for_roadmap(db, roadmap_request_id)
        return True, "Quizzes regenerated successfully"

    except Exception as e:
        logger.error(f"Error regenerating quizzes: {str(e)}")
        return False, str(e)
    finally:
        db.close()


# Optional: Generate quiz for a specific topic
async def generate_quiz_for_topic(topic_id: int):
    """Generate quiz for a specific topic"""
    from app.db.connection import SessionLocal
    
    db = SessionLocal()
    try:
        # Get the topic
        topic = db.query(RoadmapTopic).filter(RoadmapTopic.id == topic_id).first()
        
        if not topic:
            logger.error(f"Topic {topic_id} not found")
            return False, "Topic not found"

        # Generate quiz
        quiz_generator = GenerateTopicQuiz()
        success, quiz_data, error_message, generation_time = quiz_generator.generate(
            topic.content
        )

        if success:
            topic.quiz_json = quiz_data
            db.commit()
            logger.info(f"Quiz generated successfully for topic {topic_id}")
            return True, "Quiz generated successfully"
        else:
            logger.error(f"Quiz generation failed for topic {topic_id}: {error_message}")
            return False, error_message

    except Exception as e:
        logger.error(f"Error generating quiz for topic: {str(e)}")
        return False, str(e)
    finally:
        db.close()

@router.get("/roadmap/{roadmap_uuid}/status", response_model=RoadmapStatusResponse)
async def get_roadmap_status(roadmap_uuid: str, db: Session = Depends(get_db)):
    """Get the status of a roadmap generation request"""
    try:
        roadmap_request = (
            db.query(RoadmapRequest).filter(RoadmapRequest.uuid == roadmap_uuid).first()
        )

        if not roadmap_request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap not found"
            )

        progress = None
        if roadmap_request.status == "completed":
            progress = {
                "total_days": roadmap_request.total_days,
                "total_topics": roadmap_request.total_topics,
                "generation_time": roadmap_request.generation_time_seconds,
            }

        return RoadmapStatusResponse(
            success=True,
            roadmap_id=roadmap_request.uuid,
            status=roadmap_request.status,
            message=f"Roadmap is {roadmap_request.status}",
            progress=progress,
            error=roadmap_request.error_message,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting roadmap status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting roadmap status: {str(e)}",
        )


@router.get("/roadmap/all", response_model=RoadmapListResponse)
async def get_roadmaps(db: Session = Depends(get_db)):
    """Get all roadmaps"""
    try:
        roadmaps = db.query(RoadmapRequest).all()
        roadmap_list = [roadmap.to_dict() for roadmap in roadmaps]
        return RoadmapListResponse(
            success=True,
            message="Roadmaps retrieved successfully",
            roadmaps=roadmap_list,
        )
    except Exception as e:
        logger.error(f"Error getting roadmaps: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting roadmaps: {str(e)}",
        )


@router.get("/roadmap/{roadmap_uuid}", response_model=RoadmapDetailResponse)
async def get_roadmap(roadmap_uuid: str, db: Session = Depends(get_db)):
    """Get complete roadmap details"""
    try:
        # Get roadmap request with all related data
        roadmap_request = (
            db.query(RoadmapRequest).filter(RoadmapRequest.uuid == roadmap_uuid).first()
        )

        if not roadmap_request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap not found"
            )

        if roadmap_request.status != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Roadmap is not ready yet. Current status: {roadmap_request.status}",
            )

        # Get all days with topics
        days_data = []
        for day in roadmap_request.days:
            topics_data = []
            for topic in day.topics:
                topics_data.append(
                    TopicResponse(
                        topic_number=topic.topic_number,
                        title=topic.title,
                        description=topic.description,
                        estimated_time_hours=topic.estimated_time_hours,
                        content=topic.content,
                        resources=topic.resources or [],
                        topic_finished=topic.topic_finished,
                        mdx_file_path=topic.mdx_file_path,
                    )
                )

            days_data.append(
                DayResponse(
                    day=day.day_number,
                    total_time_hours=day.total_time_hours,
                    topics=topics_data,
                )
            )

        return RoadmapDetailResponse(
            success=True,
            message="Roadmap retrieved successfully",
            roadmap_id=roadmap_request.uuid,
            status=roadmap_request.status,
            created_at=roadmap_request.created_at,
            summary=roadmap_request.summary,
            total_days=roadmap_request.total_days,
            total_topics=roadmap_request.total_topics,
            context=roadmap_request.extracted_context,
            days=days_data,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting roadmap: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting roadmap: {str(e)}",
        )


@router.get("/roadmap/{roadmap_uuid}/day/{day_number}", response_model=RoadmapResponse)
async def get_day_topics(
    roadmap_uuid: str, day_number: int, db: Session = Depends(get_db)
):
    """Get topics for a specific day"""
    try:
        # Get roadmap request
        roadmap_request = (
            db.query(RoadmapRequest).filter(RoadmapRequest.uuid == roadmap_uuid).first()
        )

        if not roadmap_request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap not found"
            )

        if roadmap_request.status != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Roadmap is not ready yet. Current status: {roadmap_request.status}",
            )

        # Get specific day
        day = (
            db.query(RoadmapDay)
            .filter(
                RoadmapDay.roadmap_request_id == roadmap_request.id,
                RoadmapDay.day_number == day_number,
            )
            .first()
        )

        if not day:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Day {day_number} not found in this roadmap",
            )

        # Get topics for this day
        topics_data = []
        for topic in day.topics:
            topics_data.append(
                {
                    "topic_number": topic.topic_number,
                    "title": topic.title,
                    "description": topic.description,
                    "estimated_time_hours": topic.estimated_time_hours,
                    "content": topic.content,
                    "resources": topic.resources or [],
                    "topic_finished": topic.topic_finished,
                    "mdx_file_path": topic.mdx_file_path,
                }
            )

        return RoadmapResponse(
            success=True,
            message=f"Day {day_number} topics retrieved successfully",
            data={
                "day": day.day_number,
                "total_time_hours": day.total_time_hours,
                "topics": topics_data,
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting day topics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting day topics: {str(e)}",
        )


@router.get("/roadmap/{roadmap_uuid}/topic/{topic_number}/mdx")
async def get_topic_mdx_content(
    roadmap_uuid: str, topic_number: int, db: Session = Depends(get_db)
):
    """Get MDX content for a specific topic"""
    try:
        # Get roadmap request
        roadmap_request = (
            db.query(RoadmapRequest).filter(RoadmapRequest.uuid == roadmap_uuid).first()
        )

        if not roadmap_request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap not found"
            )

        # Get topic
        topic = (
            db.query(RoadmapTopic)
            .join(RoadmapDay)
            .filter(
                RoadmapDay.roadmap_request_id == roadmap_request.id,
                RoadmapTopic.topic_number == topic_number,
            )
            .first()
        )

        if not topic:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Topic {topic_number} not found in this roadmap",
            )

        # Try to read from file first, fallback to database content
        content = topic.content
        if topic.mdx_file_path:
            success, file_content, error = mdx_manager.read_mdx_content(
                topic.mdx_file_path
            )
            if success and file_content:
                content = file_content

        return {
            "success": True,
            "topic_number": topic.topic_number,
            "title": topic.title,
            "mdx_content": content,
            "file_path": topic.mdx_file_path,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting topic MDX content: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting topic MDX content: {str(e)}",
        )


@router.get("/roadmap/{roadmap_uuid}/topic/{topic_number}/quiz", response_model=QuizResponse)
async def get_topic_quiz(
    roadmap_uuid: str, topic_number: int, db: Session = Depends(get_db)
):
    """Get quiz for a specific topic"""
    try:
        # Get roadmap request
        roadmap_request = (
            db.query(RoadmapRequest).filter(RoadmapRequest.uuid == roadmap_uuid).first()
        )

        if not roadmap_request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap not found"
            )

        # Get the topic
        topic = (
            db.query(RoadmapTopic)
            .join(RoadmapDay)
            .filter(
                RoadmapDay.roadmap_request_id == roadmap_request.id,
                RoadmapTopic.topic_number == topic_number,
            )
            .first()
        )

        if not topic:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Topic {topic_number} not found in this roadmap",
            )

        # Check if quiz exists
        if not topic.quiz_json:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Quiz not available for this topic",
            )

        # Check if quiz has error
        if isinstance(topic.quiz_json, dict) and "error" in topic.quiz_json:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Quiz generation failed: {topic.quiz_json.get('error', 'Unknown error')}",
            )

        return QuizResponse(
            success=True,
            message="Quiz retrieved successfully",
            quiz_data=topic.quiz_json,
            topic_id=topic.id,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting topic quiz: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting topic quiz: {str(e)}",
        )


@router.post("/roadmap/{roadmap_uuid}/topic/{topic_number}/complete", response_model=TopicCompletionResponse)
async def mark_topic_complete(
    roadmap_uuid: str,
    topic_number: int,
    completion_request: TopicCompletionRequest,
    db: Session = Depends(get_db),
):
    """Mark a topic as completed or not completed"""
    try:
        # Get roadmap request
        roadmap_request = (
            db.query(RoadmapRequest).filter(RoadmapRequest.uuid == roadmap_uuid).first()
        )

        if not roadmap_request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap not found"
            )

        # Get the topic
        topic = (
            db.query(RoadmapTopic)
            .join(RoadmapDay)
            .filter(
                RoadmapDay.roadmap_request_id == roadmap_request.id,
                RoadmapTopic.topic_number == topic_number,
            )
            .first()
        )

        if not topic:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Topic {topic_number} not found in this roadmap",
            )

        # Update completion status
        topic.topic_finished = completion_request.completed
        db.commit()

        return TopicCompletionResponse(
            success=True,
            message=f"Topic marked as {'completed' if completion_request.completed else 'not completed'}",
            topic_id=topic.id,
            completed=completion_request.completed,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating topic completion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating topic completion: {str(e)}",
        )


@router.post("/roadmap/{roadmap_uuid}/topic/{topic_number}/regenerate-quiz", response_model=QuizResponse)
async def regenerate_topic_quiz(
    roadmap_uuid: str, topic_number: int, db: Session = Depends(get_db)
):
    """Regenerate quiz for a specific topic"""
    try:
        # Get roadmap request
        roadmap_request = (
            db.query(RoadmapRequest).filter(RoadmapRequest.uuid == roadmap_uuid).first()
        )

        if not roadmap_request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap not found"
            )

        # Get the topic
        topic = (
            db.query(RoadmapTopic)
            .join(RoadmapDay)
            .filter(
                RoadmapDay.roadmap_request_id == roadmap_request.id,
                RoadmapTopic.topic_number == topic_number,
            )
            .first()
        )

        if not topic:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Topic {topic_number} not found in this roadmap",
            )

        # Generate quiz
        from app.core.roadmap.generate_topic_quiz import GenerateTopicQuiz

        quiz_generator = GenerateTopicQuiz()
        success, quiz_data, error_message, generation_time = quiz_generator.generate(
            topic.content
        )

        if success:
            topic.quiz_json = quiz_data
            db.commit()
            logger.info(f"Quiz regenerated successfully for topic {topic.id}")

            return QuizResponse(
                success=True,
                message="Quiz regenerated successfully",
                quiz_data=quiz_data,
                topic_id=topic.id,
            )
        else:
            logger.error(f"Quiz regeneration failed for topic {topic.id}: {error_message}")
            topic.quiz_json = {"error": error_message, "generated_at": None}
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Quiz generation failed: {error_message}",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error regenerating topic quiz: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error regenerating topic quiz: {str(e)}",
        )
