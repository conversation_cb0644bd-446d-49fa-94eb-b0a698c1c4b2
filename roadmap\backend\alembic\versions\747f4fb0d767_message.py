"""message

Revision ID: 747f4fb0d767
Revises: 
Create Date: 2025-08-12 13:33:52.602221

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '747f4fb0d767'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_mdx_files_id'), table_name='mdx_files')
    op.drop_table('mdx_files')
    op.drop_index(op.f('ix_roadmap_requests_id'), table_name='roadmap_requests')
    op.drop_index(op.f('ix_roadmap_requests_uuid'), table_name='roadmap_requests')
    op.drop_table('roadmap_requests')
    op.drop_index(op.f('ix_roadmap_topics_id'), table_name='roadmap_topics')
    op.drop_table('roadmap_topics')
    op.drop_index(op.f('ix_roadmap_days_id'), table_name='roadmap_days')
    op.drop_table('roadmap_days')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('roadmap_days',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('roadmap_days_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('roadmap_request_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('day_number', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('total_time_hours', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['roadmap_request_id'], ['roadmap_requests.id'], name='roadmap_days_roadmap_request_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='roadmap_days_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_roadmap_days_id'), 'roadmap_days', ['id'], unique=False)
    op.create_table('roadmap_topics',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('roadmap_topics_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('day_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('topic_number', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(length=500), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('estimated_time_hours', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('resources', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('mdx_file_path', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['day_id'], ['roadmap_days.id'], name='roadmap_topics_day_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='roadmap_topics_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_roadmap_topics_id'), 'roadmap_topics', ['id'], unique=False)
    op.create_table('roadmap_requests',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('roadmap_requests_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('uuid', sa.VARCHAR(length=36), autoincrement=False, nullable=True),
    sa.Column('age', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('current_field', sa.VARCHAR(length=200), autoincrement=False, nullable=True),
    sa.Column('current_role', sa.VARCHAR(length=200), autoincrement=False, nullable=True),
    sa.Column('days_per_week', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('hours_per_day', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('knowledge_description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('knowledge_level', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('topic', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('topic_reason', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('total_duration', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('generation_time_seconds', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('extracted_context', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('summary', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('total_days', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_topics', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='roadmap_requests_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_roadmap_requests_uuid'), 'roadmap_requests', ['uuid'], unique=True)
    op.create_index(op.f('ix_roadmap_requests_id'), 'roadmap_requests', ['id'], unique=False)
    op.create_table('mdx_files',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('roadmap_request_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('file_path', sa.VARCHAR(length=500), autoincrement=False, nullable=False),
    sa.Column('file_name', sa.VARCHAR(length=200), autoincrement=False, nullable=False),
    sa.Column('topic_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('file_size_bytes', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('content_hash', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['roadmap_request_id'], ['roadmap_requests.id'], name=op.f('mdx_files_roadmap_request_id_fkey')),
    sa.ForeignKeyConstraint(['topic_id'], ['roadmap_topics.id'], name=op.f('mdx_files_topic_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('mdx_files_pkey')),
    sa.UniqueConstraint('file_path', name=op.f('mdx_files_file_path_key'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_mdx_files_id'), 'mdx_files', ['id'], unique=False)
    # ### end Alembic commands ###
