from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    Float,
    DateTime,
    ForeignKey,
    JSON,
    Boolean,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.connection import Base
import uuid


class RoadmapRequest(Base):
    """Model for storing roadmap generation requests and metadata"""

    __tablename__ = "roadmap_requests"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(
        String(36), unique=True, index=True, default=lambda: str(uuid.uuid4())
    )

    # User input data
    age = Column(String(50))
    current_field = Column(String(200))
    current_role = Column(String(200))
    days_per_week = Column(Integer)
    hours_per_day = Column(Integer)
    knowledge_description = Column(Text)
    knowledge_level = Column(Integer)
    topic = Column(String(500))
    topic_reason = Column(Text)
    total_duration = Column(String(100))

    # Generation metadata
    status = Column(
        String(50), default="pending"
    )  # pending, generating, completed, failed
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    generation_time_seconds = Column(Float, nullable=True)
    error_message = Column(Text, nullable=True)

    # Context and summary
    extracted_context = Column(JSON, nullable=True)
    summary = Column(Text, nullable=True)
    total_days = Column(Integer, nullable=True)
    total_topics = Column(Integer, nullable=True)

    # Relationships
    days = relationship(
        "RoadmapDay", back_populates="roadmap_request", cascade="all, delete-orphan"
    )
    mdx_files = relationship(
        "MDXFile", back_populates="roadmap_request", cascade="all, delete-orphan"
    )

    def to_dict(self):
        return {
            "id": self.id,
            "uuid": self.uuid,
            "age": self.age,
            "current_field": self.current_field,
            "current_role": self.current_role,
            "days_per_week": self.days_per_week,
            "hours_per_day": self.hours_per_day,
            "knowledge_description": self.knowledge_description,
            "knowledge_level": self.knowledge_level,
            "topic": self.topic,
            "topic_reason": self.topic_reason,
            "total_duration": self.total_duration,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "generation_time_seconds": self.generation_time_seconds,
            "error_message": self.error_message,
            "extracted_context": self.extracted_context,
            "summary": self.summary,
            "total_days": self.total_days,
            "total_topics": self.total_topics,
        }


class RoadmapDay(Base):
    """Model for storing individual days in a roadmap"""

    __tablename__ = "roadmap_days"

    id = Column(Integer, primary_key=True, index=True)
    roadmap_request_id = Column(
        Integer, ForeignKey("roadmap_requests.id"), nullable=False
    )

    day_number = Column(Integer, nullable=False)
    total_time_hours = Column(Float, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    roadmap_request = relationship("RoadmapRequest", back_populates="days")
    topics = relationship(
        "RoadmapTopic", back_populates="day", cascade="all, delete-orphan"
    )


class RoadmapTopic(Base):
    """Model for storing individual topics within a day"""

    __tablename__ = "roadmap_topics"

    id = Column(Integer, primary_key=True, index=True)
    day_id = Column(Integer, ForeignKey("roadmap_days.id"), nullable=False)

    topic_number = Column(Integer, nullable=False)
    title = Column(String(500), nullable=False)
    description = Column(Text)
    estimated_time_hours = Column(Float, nullable=False)
    content = Column(Text)  # MDX content
    resources = Column(JSON)  # List of resource URLs
    topic_finished = Column(Boolean, default=False)

    quiz_json = Column(JSON, nullable=True)
    # MDX file reference
    mdx_file_path = Column(String(500), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    day = relationship("RoadmapDay", back_populates="topics")


class MDXFile(Base):
    """Model for tracking MDX files saved to local storage"""

    __tablename__ = "mdx_files"

    id = Column(Integer, primary_key=True, index=True)
    roadmap_request_id = Column(
        Integer, ForeignKey("roadmap_requests.id"), nullable=False
    )

    file_path = Column(String(500), nullable=False, unique=True)
    file_name = Column(String(200), nullable=False)
    topic_id = Column(Integer, ForeignKey("roadmap_topics.id"), nullable=True)

    # File metadata
    file_size_bytes = Column(Integer, nullable=True)
    content_hash = Column(String(64), nullable=True)  # SHA-256 hash
    is_active = Column(Boolean, default=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    roadmap_request = relationship("RoadmapRequest", back_populates="mdx_files")
    topic = relationship("RoadmapTopic", foreign_keys=[topic_id])
