from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain.prompts import PromptTemplate
import json
import time
import logging
from typing import Dict, Any, Tuple, Optional
import re
from app.core.settings import settings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GenerateRoadmap:
    def __init__(
        self,
        api_key: str = settings.google_api_key,
        model: str = "gemini-1.5-flash",
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        self.llm = ChatGoogleGenerativeAI(
            model=model,
            google_api_key=api_key,
            temperature=0.7,
            max_tokens=8192
        )
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def generate(self, payload: dict) -> Tuple[bool, Optional[Dict[Any, Any]], Optional[str], float]:
        """
        Generate roadmap using LLM with retry logic and validation

        Args:
            payload: User input data

        Returns:
            Tuple of (success, parsed_data, error_message, generation_time)
        """
        start_time = time.time()

        try:
            # Validate input payload
            validation_error = self._validate_payload(payload)
            if validation_error:
                return False, None, f"Validation error: {validation_error}", 0.0

            # Transform payload
            new_payload = {
                "age": payload["age"],
                "field": payload["currentField"],
                "role": payload["currentRole"],
                "days_per_week": payload["daysPerWeek"],
                "hours_per_day": payload["hoursPerDay"],
                "current_knowledge": payload["knowledgeDescription"],
                "knowledge_level": payload["knowledgeLevel"],
                "topic": payload["topic"],
                "reason": payload["topicReason"],
                "duration": payload["totalDuration"],
                "start_date": "2024-10-01",
            }

            # Generate with retry logic
            for attempt in range(self.max_retries):
                try:
                    logger.info(f"Generating roadmap, attempt {attempt + 1}/{self.max_retries}")

                    prompt = PromptTemplate(
                        input_variables=[
                            "age", "field", "role", "days_per_week", "hours_per_day",
                            "current_knowledge", "knowledge_level", "topic", "reason",
                            "duration", "start_date",
                        ],
                        template=prompt_template,
                    )

                    formatted_prompt = prompt.format(**new_payload)
                    messages = [
                        SystemMessage(content=system_prompt),
                        HumanMessage(content=formatted_prompt),
                    ]

                    response = self.llm.invoke(messages)

                    # Parse and validate response
                    success, parsed_data, parse_error = self._parse_and_validate_response(response.content)

                    if success:
                        generation_time = time.time() - start_time
                        logger.info(f"Roadmap generated successfully in {generation_time:.2f} seconds")
                        return True, parsed_data, None, generation_time
                    else:
                        logger.warning(f"Attempt {attempt + 1} failed: {parse_error}")
                        if attempt < self.max_retries - 1:
                            time.sleep(self.retry_delay)
                        else:
                            return False, None, f"Failed after {self.max_retries} attempts. Last error: {parse_error}", time.time() - start_time

                except Exception as e:
                    logger.error(f"Attempt {attempt + 1} failed with exception: {str(e)}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        return False, None, f"Generation failed after {self.max_retries} attempts: {str(e)}", time.time() - start_time

        except Exception as e:
            logger.error(f"Unexpected error in generate: {str(e)}")
            return False, None, f"Unexpected error: {str(e)}", time.time() - start_time

    def _validate_payload(self, payload: dict) -> Optional[str]:
        """Validate input payload"""
        required_fields = [
            "age", "currentField", "currentRole", "daysPerWeek", "hoursPerDay",
            "knowledgeDescription", "knowledgeLevel", "topic", "topicReason", "totalDuration"
        ]

        for field in required_fields:
            if field not in payload or not payload[field]:
                return f"Missing required field: {field}"

        # Validate numeric fields
        try:
            if not isinstance(payload["daysPerWeek"], int) or payload["daysPerWeek"] < 1 or payload["daysPerWeek"] > 7:
                return "daysPerWeek must be between 1 and 7"
            if not isinstance(payload["hoursPerDay"], int) or payload["hoursPerDay"] < 1 or payload["hoursPerDay"] > 24:
                return "hoursPerDay must be between 1 and 24"
            if not isinstance(payload["knowledgeLevel"], int) or payload["knowledgeLevel"] < 0 or payload["knowledgeLevel"] > 10:
                return "knowledgeLevel must be between 0 and 10"
        except (TypeError, ValueError):
            return "Invalid numeric values in payload"

        return None

    def _parse_and_validate_response(self, response_content: str) -> Tuple[bool, Optional[Dict[Any, Any]], Optional[str]]:
        """Parse and validate LLM response"""
        try:
            # Clean the response
            cleaned = self._clean_json_string(response_content)

            # Parse JSON
            parsed_data = json.loads(cleaned)

            # Validate structure
            if "plan" not in parsed_data:
                return False, None, "Response missing 'plan' field"

            if not isinstance(parsed_data["plan"], list) or len(parsed_data["plan"]) == 0:
                return False, None, "Plan must be a non-empty list"

            # Validate each day
            for day_idx, day in enumerate(parsed_data["plan"]):
                if not isinstance(day, dict):
                    return False, None, f"Day {day_idx + 1} is not a valid object"

                required_day_fields = ["day", "total_time_hours", "topics"]
                for field in required_day_fields:
                    if field not in day:
                        return False, None, f"Day {day_idx + 1} missing field: {field}"

                # Validate topics
                if not isinstance(day["topics"], list) or len(day["topics"]) == 0:
                    return False, None, f"Day {day_idx + 1} must have at least one topic"

                for topic_idx, topic in enumerate(day["topics"]):
                    if not isinstance(topic, dict):
                        return False, None, f"Day {day_idx + 1}, Topic {topic_idx + 1} is not a valid object"

                    required_topic_fields = ["topic_number", "title", "description", "estimated_time_hours", "content", "resources"]
                    for field in required_topic_fields:
                        if field not in topic:
                            return False, None, f"Day {day_idx + 1}, Topic {topic_idx + 1} missing field: {field}"

            return True, parsed_data, None

        except json.JSONDecodeError as e:
            return False, None, f"Invalid JSON response: {str(e)}"
        except Exception as e:
            return False, None, f"Error parsing response: {str(e)}"

    def _clean_json_string(self, s: str) -> str:
        """Clean JSON string by removing markdown formatting and fixing common issues."""
        import re

        # Remove triple backticks and 'json' label
        s = s.strip().strip("`")
        s = re.sub(r"^json\s*", "", s, flags=re.MULTILINE)

        # Remove control characters that break JSON
        s = re.sub(r'[\x00-\x1F\x7F]', '', s)

        # Fix invalid backslashes (any \ not part of a valid escape)
        s = re.sub(r'(?<!\\)\\(?![\\/"bfnrtu])', r'\\\\', s)

        # Remove trailing commas in objects/arrays
        s = re.sub(r',(\s*[}\]])', r'\1', s)

        # Fix escaped newlines in content fields
        s = re.sub(
            r'("content":\s*")(.*?)("(?=\s*,\s*"resources"))',
            lambda m: m.group(1) + m.group(2).replace("\n", "\\n") + m.group(3),
            s,
            flags=re.DOTALL,
        )

        return s



prompt_template = """
Here is the student's learning info:
Age: {age}  
Field/Industry: {field}  
Current Role: {role}  
Days per Week: {days_per_week}  
Hours per Day: {hours_per_day}  
Describe What You Know: {current_knowledge}  
Knowledge Level (0–10): {knowledge_level}  
Topic to Learn: {topic}  
Reason for Learning: {reason}  
Total Duration (weeks/months): {duration}  

TASK:
1. Create a **roadmap** for learning the topic within the given total duration.
2. Break the roadmap into a **course structure** with clear modules or sections.
3. Assign each module to specific **days** based on the student's availability (`daysPerWeek` × `hoursPerDay`).
4. Within each day, split content into **topics** based on actual complexity and estimated learning time (not fixed time rules).
5. If a topic is too large for one day, split it into sequential parts (e.g., "Part 1", "Part 2") to ensure complete coverage.
6. Ensure topics build progressively from beginner-friendly foundations to advanced mastery.
7. Each topic must include:
   - `topic_number` (sequential across the whole plan)
   - `title` (clear and descriptive)
   - `description` (short summary)
   - `estimated_time_hours` (realistic time needed for the topic)
   - `content` (2000–4000 words, **mdx blog-style format** with real-world examples, analogies, applied exercises, and contextual explanations)
   - `resources` (1–5 credible references: links, books, videos)
8. Each day must include:
   - `day` (number)
   - `total_time_hours` (sum of estimated time for all topics that day)
   - `topics` (list of topic objects)


"""
system_prompt = """SYSTEM:
You are a distinguished university professor and a top-tier technical blogger, specializing in computer science and AI.  
Your task is to create a **detailed, example-rich learning plan** that teaches the given topic in a structured, engaging, and academically rigorous way.

TOPIC DISTRIBUTION RULES:
- Base topic duration on **content complexity** and **depth**, not a fixed minutes-per-topic rule.
- Large topics should be **split into parts** so each is manageable and deeply covered.
- Each day's `total_time_hours` should reflect the sum of all topics assigned for that day.

WRITING STYLE:
- Combine **academic depth** with a conversational, blog-style tone.
- Use **real-world analogies**, **contextual insights**, and **hands-on examples**.
- Format `content` in **mdx** with:
  - Headings & subheadings
  - Lists
  - Tables (if relevant)
  - Highlighted key points
- End each topic with **references** or **practical exercises**.

SCHEMA:
{{
  "plan": [
    {{
      "day": number,
      "total_time_hours": number,
      "topics": [
        {{
          "topic_number": number,
          "title": string,
          "description": string,
          "estimated_time_hours": number,
          "content": "string(mdx format)",
          "resources": [string]
        }}
      ]
    }}
  ]
}}
"""
